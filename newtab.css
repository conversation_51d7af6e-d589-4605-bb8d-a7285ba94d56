/* Chrome-style New Tab with Hacker Theme */

:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --card-bg: #1e1e1e;
    --accent-green: #00ff41;
    --accent-cyan: #00ffff;
    --accent-red: #ff0040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;
    --border-color: #333333;
    --hover-bg: rgba(255, 255, 255, 0.1);
    --glow-green: 0 0 10px #00ff41;
    --glow-cyan: 0 0 10px #00ffff;
    --chrome-blue: #4285f4;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    min-height: 100vh;
    height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

/* Matrix Background */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(0, 255, 65, 0.02) 0%, 
        rgba(0, 0, 0, 0.98) 50%, 
        rgba(0, 255, 255, 0.02) 100%);
    z-index: -1;
    animation: matrixFlow 30s linear infinite;
}

@keyframes matrixFlow {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* Chrome Header */
.chrome-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 24px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-section i {
    color: var(--accent-green);
    font-size: 20px;
    text-shadow: var(--glow-green);
}

.browser-name {
    font-family: 'Fira Code', monospace;
    font-weight: 600;
    color: var(--accent-green);
    text-shadow: var(--glow-green);
}

.user-section {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    background: rgba(0, 255, 65, 0.1);
    border-radius: 20px;
    border: 1px solid var(--accent-green);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--accent-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-bg);
}

.user-name {
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    color: var(--accent-green);
}

/* Main Content */
.main-content {
    width: 100%;
    margin: 0;
    padding: 20px;
    min-height: calc(100vh - 140px);
    display: flex;
    flex-direction: column;
}

/* Search Section */
.search-section {
    text-align: center;
    margin-bottom: 40px;
    flex-shrink: 0;
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-logo {
    margin-bottom: 20px;
}

.hacker-logo {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-family: 'Fira Code', monospace;
    font-size: 36px;
    font-weight: bold;
    color: var(--accent-green);
    text-shadow: var(--glow-green);
}

.hacker-logo i {
    font-size: 32px;
}

.search-box-container {
    margin-bottom: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 24px;
    padding: 12px 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.search-box:hover,
.search-box:focus-within {
    border-color: var(--accent-green);
    box-shadow: var(--glow-green);
}

.search-icon {
    color: var(--text-muted);
    margin-right: 12px;
}

#search-input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 16px;
    font-family: inherit;
}

#search-input::placeholder {
    color: var(--text-muted);
}

.search-actions {
    display: flex;
    gap: 8px;
}

.search-actions button {
    background: none;
    border: none;
    color: var(--text-muted);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-actions button:hover {
    background: var(--hover-bg);
    color: var(--accent-cyan);
}

.search-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.search-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--hover-bg);
    border-color: var(--accent-cyan);
}

.hacker-btn {
    background: var(--accent-green);
    color: var(--primary-bg);
    border-color: var(--accent-green);
    font-weight: 600;
}

.hacker-btn:hover {
    background: var(--accent-cyan);
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
}

/* Shortcuts Section */
.shortcuts-section {
    margin-bottom: 40px;
    flex-shrink: 0;
}

.shortcuts-container {
    text-align: center;
    width: 100%;
}

.shortcuts-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 20px;
    margin-bottom: 30px;
    color: var(--accent-cyan);
    text-shadow: var(--glow-cyan);
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto 30px;
    padding: 0 20px;
}

.shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 10px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.shortcut-item:hover {
    background: var(--hover-bg);
    border-color: var(--accent-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 255, 65, 0.2);
}

.shortcut-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-green);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 24px;
    color: var(--primary-bg);
    box-shadow: var(--glow-green);
}

.shortcut-name {
    font-size: 12px;
    text-align: center;
    line-height: 1.3;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.add-shortcut-btn {
    background: var(--card-bg);
    border: 2px dashed var(--border-color);
    color: var(--text-muted);
    padding: 20px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-shortcut-btn:hover {
    border-color: var(--accent-green);
    color: var(--accent-green);
    background: rgba(0, 255, 65, 0.1);
}

/* Quick Panels */
.quick-panels {
    margin-bottom: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.panel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    flex: 1;
}

.quick-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.quick-panel:hover {
    border-color: var(--accent-green);
    box-shadow: 0 4px 20px rgba(0, 255, 65, 0.1);
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.panel-header i {
    color: var(--accent-green);
    font-size: 18px;
}

.panel-header h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.panel-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.panel-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.panel-link:hover {
    background: var(--hover-bg);
    color: var(--accent-cyan);
}

.panel-link i {
    width: 16px;
    color: var(--accent-green);
}

/* Chrome Footer */
.chrome-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    flex-shrink: 0;
}

.footer-left,
.footer-right {
    display: flex;
    gap: 20px;
}

.footer-link {
    color: var(--text-muted);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.footer-link:hover {
    color: var(--accent-cyan);
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 14px;
}

.footer-info i {
    color: var(--accent-green);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    border: 2px solid var(--accent-green);
    border-radius: 12px;
    margin: 10% auto;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--glow-green);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--accent-green);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 0, 64, 0.2);
    color: var(--accent-red);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    background: var(--primary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 12px;
    border-radius: 6px;
    font-family: inherit;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--accent-green);
    box-shadow: var(--glow-green);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-cancel,
.btn-save {
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: var(--hover-bg);
}

.btn-save {
    background: var(--accent-green);
    border: 1px solid var(--accent-green);
    color: var(--primary-bg);
}

.btn-save:hover {
    background: var(--accent-cyan);
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .shortcuts-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        max-width: 1000px;
    }

    .panel-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        max-width: 1000px;
    }
}

@media (max-width: 768px) {
    .chrome-header {
        padding: 8px 16px;
    }

    .main-content {
        padding: 15px 10px;
    }

    .hacker-logo {
        font-size: 36px;
    }

    .hacker-logo i {
        font-size: 30px;
    }

    .shortcuts-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 15px;
        padding: 0 10px;
    }

    .panel-grid {
        grid-template-columns: 1fr;
        padding: 0 10px;
    }

    .chrome-footer {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px;
    }

    .search-container {
        max-width: 100%;
    }

    .search-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (min-width: 1400px) {
    .shortcuts-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .panel-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
