<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HackerBrowser Demo Page</title>
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #00ff41;
            font-family: 'Courier New', monospace;
            padding: 40px;
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            border: 2px solid #00ff41;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
            background: rgba(0, 0, 0, 0.8);
        }
        
        h1 {
            text-align: center;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: rgba(0, 255, 65, 0.05);
        }
        
        .demo-section h2 {
            color: #00ff41;
            border-bottom: 1px solid #00ff41;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            margin: 10px 0;
            padding: 8px 0;
            border-left: 3px solid #00ff41;
            padding-left: 15px;
        }
        
        .feature-list li::before {
            content: "▶ ";
            color: #00ffff;
            font-weight: bold;
        }
        
        .code-block {
            background: #000;
            border: 1px solid #00ff41;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .success-message {
            background: rgba(0, 255, 65, 0.2);
            border: 1px solid #00ff41;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .warning-message {
            background: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffff00;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #ffff00;
        }
        
        .navigation-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .nav-card {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-card:hover {
            background: rgba(0, 255, 255, 0.2);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }
        
        .nav-card h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }
        
        .ascii-banner {
            text-align: center;
            font-size: 12px;
            line-height: 1;
            color: #00ff41;
            margin: 30px 0;
            text-shadow: 0 0 5px #00ff41;
        }
        
        @keyframes glow {
            0%, 100% { text-shadow: 0 0 5px #00ff41; }
            50% { text-shadow: 0 0 20px #00ff41, 0 0 30px #00ff41; }
        }
        
        .glow-text {
            animation: glow 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 HACKERBROWSER DEMO 🔥</h1>
        
        <div class="success-message glow-text">
            ✅ SUCCESS: This page loaded successfully in the iframe!
        </div>
        
        <div class="ascii-banner">
            <pre>
    ██████╗ ███████╗███╗   ███╗ ██████╗ 
    ██╔══██╗██╔════╝████╗ ████║██╔═══██╗
    ██║  ██║█████╗  ██╔████╔██║██║   ██║
    ██║  ██║██╔══╝  ██║╚██╔╝██║██║   ██║
    ██████╔╝███████╗██║ ╚═╝ ██║╚██████╔╝
    ╚═════╝ ╚══════╝╚═╝     ╚═╝ ╚═════╝ 
            </pre>
        </div>
        
        <div class="demo-section">
            <h2>🎯 Browser Functionality Test</h2>
            <p>This demo page proves that the HackerBrowser iframe system works correctly for compatible websites.</p>
            
            <div class="warning-message">
                <strong>⚠️ Important:</strong> Most security tools and hacking websites block iframe loading for security reasons. This is normal behavior and not a bug in the browser.
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔧 Why External Links?</h2>
            <ul class="feature-list">
                <li>Security tools implement X-Frame-Options headers</li>
                <li>Content Security Policy (CSP) blocks iframe embedding</li>
                <li>This protects against clickjacking attacks</li>
                <li>External opening ensures full functionality</li>
                <li>Better user experience with native browser features</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Navigation Options</h2>
            <div class="navigation-demo">
                <div class="nav-card" onclick="testNavigation('new-tab')">
                    <h3>🆕 New Tab</h3>
                    <p>Ctrl+Click or right-click menu</p>
                </div>
                <div class="nav-card" onclick="testNavigation('current-tab')">
                    <h3>🌐 Current Tab</h3>
                    <p>Direct navigation option</p>
                </div>
                <div class="nav-card" onclick="testNavigation('iframe')">
                    <h3>🖼️ Iframe Test</h3>
                    <p>Try loading in frame</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📊 Browser Features Working</h2>
            <div class="code-block">
✅ Terminal emulator with command simulation
✅ Port scanner with realistic output  
✅ Encoder/decoder with multiple formats
✅ Notes system with persistent storage
✅ Real-time system monitoring
✅ 600+ organized security tools
✅ Cyberpunk terminal aesthetics
✅ Keyboard shortcuts and hotkeys
✅ Context menus and navigation options
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎮 Test the Built-in Tools</h2>
            <p>Try these features in the main browser interface:</p>
            <ul class="feature-list">
                <li>Click "Terminal" in System Tools for command simulation</li>
                <li>Use "Port Scanner" to test network scanning interface</li>
                <li>Try "Encoder/Decoder" for text manipulation</li>
                <li>Take notes with the "Notes" tool</li>
                <li>Browse tool categories in the sidebar</li>
                <li>Use keyboard shortcuts (Ctrl+L, Ctrl+T, Esc)</li>
            </ul>
        </div>
        
        <div class="success-message">
            🎯 HackerBrowser v2.1.0 is working perfectly!<br>
            External link opening is the intended behavior for security tools.
        </div>
    </div>
    
    <script>
        function testNavigation(type) {
            const messages = {
                'new-tab': 'Opening in new tab - this is how security tools work!',
                'current-tab': 'This would navigate the current tab',
                'iframe': 'This page works in iframe because it allows it'
            };
            
            alert('🔧 ' + messages[type]);
            
            if (type === 'new-tab') {
                window.open('https://github.com/', '_blank');
            }
        }
        
        // Add some dynamic effects
        setInterval(() => {
            const glowElements = document.querySelectorAll('.glow-text');
            glowElements.forEach(el => {
                el.style.textShadow = Math.random() > 0.5 ? 
                    '0 0 20px #00ff41, 0 0 30px #00ff41' : 
                    '0 0 5px #00ff41';
            });
        }, 1000);
        
        console.log('🔥 HackerBrowser Demo Page Loaded Successfully!');
        console.log('📊 All browser features are working correctly.');
        console.log('🚀 External link opening is intentional for security tools.');
    </script>
</body>
</html>
