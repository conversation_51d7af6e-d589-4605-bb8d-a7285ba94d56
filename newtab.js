// Chrome-style New Tab with Hacker Theme
class HackerNewTab {
    constructor() {
        this.shortcuts = this.loadShortcuts();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.renderShortcuts();
        this.setupSearch();
        this.addMatrixEffect();
    }

    setupEventListeners() {
        // Search functionality
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        document.getElementById('google-search').addEventListener('click', () => {
            this.performSearch('google');
        });

        document.getElementById('feeling-lucky').addEventListener('click', () => {
            this.performSearch('lucky');
        });

        document.getElementById('hacker-search').addEventListener('click', () => {
            this.performSearch('hacker');
        });

        // Add shortcut functionality
        document.getElementById('add-shortcut').addEventListener('click', () => {
            this.showAddShortcutModal();
        });

        // Modal functionality
        document.querySelector('.modal-close').addEventListener('click', () => {
            this.hideAddShortcutModal();
        });

        document.getElementById('cancel-shortcut').addEventListener('click', () => {
            this.hideAddShortcutModal();
        });

        document.getElementById('save-shortcut').addEventListener('click', () => {
            this.saveShortcut();
        });

        // Footer links
        document.querySelector('.footer-right a[href="#"]').addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'index.html';
        });

        // Voice search simulation
        document.querySelector('.voice-search').addEventListener('click', () => {
            this.simulateVoiceSearch();
        });

        // Camera search simulation
        document.querySelector('.camera-search').addEventListener('click', () => {
            this.simulateCameraSearch();
        });
    }

    loadShortcuts() {
        const defaultShortcuts = [
            { name: 'Kali Tools', url: 'https://www.kali.org/tools/', icon: 'fas fa-terminal' },
            { name: 'ExploitDB', url: 'https://www.exploit-db.com/', icon: 'fas fa-database' },
            { name: 'Shodan', url: 'https://www.shodan.io/', icon: 'fas fa-satellite' },
            { name: 'GitHub', url: 'https://github.com/', icon: 'fab fa-github' },
            { name: 'HackTheBox', url: 'https://www.hackthebox.eu/', icon: 'fas fa-cube' },
            { name: 'TryHackMe', url: 'https://tryhackme.com/', icon: 'fas fa-flag' },
            { name: 'Burp Suite', url: 'https://portswigger.net/burp', icon: 'fas fa-shield-alt' },
            { name: 'Metasploit', url: 'https://www.metasploit.com/', icon: 'fas fa-rocket' },
            { name: 'OWASP', url: 'https://owasp.org/', icon: 'fas fa-lock' },
            { name: 'Nmap', url: 'https://nmap.org/', icon: 'fas fa-network-wired' }
        ];

        const saved = localStorage.getItem('hackerShortcuts');
        return saved ? JSON.parse(saved) : defaultShortcuts;
    }

    saveShortcuts() {
        localStorage.setItem('hackerShortcuts', JSON.stringify(this.shortcuts));
    }

    renderShortcuts() {
        const grid = document.getElementById('shortcuts-grid');
        grid.innerHTML = '';

        this.shortcuts.forEach((shortcut, index) => {
            const shortcutElement = document.createElement('a');
            shortcutElement.className = 'shortcut-item';
            shortcutElement.href = shortcut.url;
            shortcutElement.target = '_blank';
            shortcutElement.innerHTML = `
                <div class="shortcut-icon">
                    <i class="${shortcut.icon}"></i>
                </div>
                <div class="shortcut-name">${shortcut.name}</div>
            `;

            // Add right-click context menu
            shortcutElement.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showShortcutContextMenu(e, index);
            });

            grid.appendChild(shortcutElement);
        });
    }

    setupSearch() {
        const searchInput = document.getElementById('search-input');
        
        // Add search suggestions (simulated)
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            if (query.length > 2) {
                this.showSearchSuggestions(query);
            } else {
                this.hideSearchSuggestions();
            }
        });
    }

    performSearch(type = 'google') {
        const query = document.getElementById('search-input').value.trim();
        if (!query) return;

        let searchUrl;
        
        switch (type) {
            case 'google':
                searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;
                break;
            case 'lucky':
                searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}&btnI=1`;
                break;
            case 'hacker':
                // Custom hacker search - searches multiple security databases
                this.performHackerSearch(query);
                return;
            default:
                searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;
        }

        window.open(searchUrl, '_blank');
    }

    performHackerSearch(query) {
        // Open multiple security-related searches in new tabs
        const hackerSearches = [
            `https://www.exploit-db.com/search?q=${encodeURIComponent(query)}`,
            `https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword=${encodeURIComponent(query)}`,
            `https://www.shodan.io/search?query=${encodeURIComponent(query)}`,
            `https://github.com/search?q=${encodeURIComponent(query)}+security`
        ];

        hackerSearches.forEach((url, index) => {
            setTimeout(() => {
                window.open(url, '_blank');
            }, index * 500); // Stagger the opening
        });

        this.showNotification('🔍 Hacker search launched across multiple databases!');
    }

    showSearchSuggestions(query) {
        // Simulate search suggestions for hacker tools
        const suggestions = [
            'nmap port scanning',
            'metasploit framework',
            'burp suite tutorial',
            'sql injection techniques',
            'xss payload examples',
            'privilege escalation linux',
            'active directory attacks',
            'osint techniques',
            'malware analysis tools',
            'penetration testing methodology'
        ].filter(suggestion => suggestion.includes(query));

        if (suggestions.length > 0) {
            console.log('Search suggestions:', suggestions.slice(0, 5));
        }
    }

    hideSearchSuggestions() {
        // Hide suggestions (implementation would show/hide suggestion dropdown)
        console.log('Hiding search suggestions');
    }

    showAddShortcutModal() {
        document.getElementById('add-shortcut-modal').style.display = 'block';
        document.getElementById('shortcut-name').focus();
    }

    hideAddShortcutModal() {
        document.getElementById('add-shortcut-modal').style.display = 'none';
        // Clear form
        document.getElementById('shortcut-name').value = '';
        document.getElementById('shortcut-url').value = '';
        document.getElementById('shortcut-icon').selectedIndex = 0;
    }

    saveShortcut() {
        const name = document.getElementById('shortcut-name').value.trim();
        const url = document.getElementById('shortcut-url').value.trim();
        const icon = document.getElementById('shortcut-icon').value;

        if (!name || !url) {
            this.showNotification('⚠️ Please fill in all fields');
            return;
        }

        // Validate URL
        try {
            new URL(url);
        } catch {
            this.showNotification('⚠️ Please enter a valid URL');
            return;
        }

        // Add new shortcut
        this.shortcuts.push({ name, url, icon });
        this.saveShortcuts();
        this.renderShortcuts();
        this.hideAddShortcutModal();
        this.showNotification(`✅ Added ${name} to shortcuts`);
    }

    showShortcutContextMenu(event, index) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.shortcut-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create context menu
        const contextMenu = document.createElement('div');
        contextMenu.className = 'shortcut-context-menu';
        contextMenu.style.cssText = `
            position: fixed;
            top: ${event.clientY}px;
            left: ${event.clientX}px;
            background: var(--card-bg);
            border: 1px solid var(--accent-green);
            border-radius: 6px;
            padding: 8px 0;
            z-index: 10000;
            box-shadow: var(--glow-green);
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            min-width: 150px;
        `;

        const menuItems = [
            { text: '🗑️ Remove', action: () => this.removeShortcut(index) },
            { text: '✏️ Edit', action: () => this.editShortcut(index) },
            { text: '🚀 Open in new tab', action: () => window.open(this.shortcuts[index].url, '_blank') }
        ];

        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: var(--text-primary);
                transition: all 0.3s ease;
            `;
            menuItem.textContent = item.text;
            
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.background = 'rgba(0, 255, 65, 0.2)';
            });
            
            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.background = 'transparent';
            });
            
            menuItem.addEventListener('click', () => {
                item.action();
                contextMenu.remove();
            });
            
            contextMenu.appendChild(menuItem);
        });

        document.body.appendChild(contextMenu);

        // Remove context menu when clicking elsewhere
        setTimeout(() => {
            document.addEventListener('click', () => {
                contextMenu.remove();
            }, { once: true });
        }, 100);
    }

    removeShortcut(index) {
        const shortcut = this.shortcuts[index];
        if (confirm(`Remove ${shortcut.name} from shortcuts?`)) {
            this.shortcuts.splice(index, 1);
            this.saveShortcuts();
            this.renderShortcuts();
            this.showNotification(`🗑️ Removed ${shortcut.name}`);
        }
    }

    editShortcut(index) {
        const shortcut = this.shortcuts[index];
        document.getElementById('shortcut-name').value = shortcut.name;
        document.getElementById('shortcut-url').value = shortcut.url;
        document.getElementById('shortcut-icon').value = shortcut.icon;
        
        // Remove the old shortcut and show modal for editing
        this.shortcuts.splice(index, 1);
        this.showAddShortcutModal();
    }

    simulateVoiceSearch() {
        this.showNotification('🎤 Voice search activated (simulation)');
        document.getElementById('search-input').placeholder = 'Listening...';
        
        setTimeout(() => {
            document.getElementById('search-input').placeholder = 'Search the web or enter URL...';
            document.getElementById('search-input').value = 'penetration testing tools';
        }, 2000);
    }

    simulateCameraSearch() {
        this.showNotification('📷 Camera search activated (simulation)');
    }

    addMatrixEffect() {
        // Add subtle matrix rain effect
        const matrixBg = document.querySelector('.matrix-bg');
        setInterval(() => {
            const opacity = Math.random() * 0.05 + 0.01;
            matrixBg.style.background = `linear-gradient(45deg, 
                rgba(0, 255, 65, ${opacity}) 0%, 
                rgba(0, 0, 0, 0.98) 50%, 
                rgba(0, 255, 255, ${opacity}) 100%)`;
        }, 3000);
    }

    showNotification(message) {
        // Create notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            border: 1px solid var(--accent-green);
            color: var(--text-primary);
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            font-family: 'Fira Code', monospace;
            box-shadow: var(--glow-green);
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HackerNewTab();
    
    // Add global keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl+K or Cmd+K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('search-input').focus();
            document.getElementById('search-input').select();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            document.getElementById('add-shortcut-modal').style.display = 'none';
        }
    });
});
