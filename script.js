// HackerBrowser v2.1.0 - Terminal Interface
class HackerBrowser {
    constructor() {
        this.currentTab = 'reconnaissance';
        this.bookmarks = this.loadBookmarks();
        this.history = [];
        this.currentUrl = 'about:hackerbrowser';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTime();
        this.loadBookmarkContent();
        this.simulateSystemStats();
        this.initMatrixEffect();
        
        // Update time every second
        setInterval(() => this.updateTime(), 1000);
        setInterval(() => this.simulateSystemStats(), 3000);
    }

    setupEventListeners() {
        // Command input
        const commandInput = document.getElementById('command-input');
        const executeBtn = document.getElementById('execute-btn');
        
        commandInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand(commandInput.value);
            }
        });
        
        executeBtn.addEventListener('click', () => {
            this.executeCommand(commandInput.value);
        });

        // URL input
        const urlInput = document.getElementById('url-input');
        const goBtn = document.getElementById('go-btn');
        
        urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.navigateToUrl(urlInput.value);
            }
        });
        
        goBtn.addEventListener('click', () => {
            this.navigateToUrl(urlInput.value);
        });

        // Navigation buttons
        document.getElementById('back-btn').addEventListener('click', () => this.goBack());
        document.getElementById('forward-btn').addEventListener('click', () => this.goForward());
        document.getElementById('refresh-btn').addEventListener('click', () => this.refresh());

        // Tab navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                this.switchTab(item.dataset.tab);
            });
        });

        // Quick links
        document.querySelectorAll('.quick-links a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToUrl(link.dataset.url);
            });
        });

        // Panel toggle
        document.querySelector('.panel-toggle').addEventListener('click', () => {
            this.toggleResourcePanel();
        });

        // System tools
        document.getElementById('terminal-btn').addEventListener('click', () => this.openTerminal());
        document.getElementById('notes-btn').addEventListener('click', () => this.openNotes());
        document.getElementById('scanner-btn').addEventListener('click', () => this.openScanner());
        document.getElementById('encoder-btn').addEventListener('click', () => this.openEncoder());

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });

        // Terminal functionality
        document.getElementById('terminal-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeTerminalCommand(e.target.value);
                e.target.value = '';
            }
        });

        // Notes functionality
        document.getElementById('save-notes').addEventListener('click', () => this.saveNotes());
        document.getElementById('clear-notes').addEventListener('click', () => this.clearNotes());

        // Scanner functionality
        document.getElementById('scan-btn').addEventListener('click', () => this.performPortScan());

        // Encoder functionality
        document.getElementById('encode-btn').addEventListener('click', () => this.encodeText());
        document.getElementById('decode-btn').addEventListener('click', () => this.decodeText());
    }

    executeCommand(command) {
        const commandInput = document.getElementById('command-input');
        
        if (!command.trim()) return;
        
        // Add terminal-style command processing
        console.log(`[COMMAND] ${command}`);
        
        // Handle different command types
        if (command.startsWith('http://') || command.startsWith('https://')) {
            this.navigateToUrl(command);
        } else if (command.startsWith('search ')) {
            const query = command.substring(7);
            this.performSearch(query);
        } else if (command === 'clear') {
            commandInput.value = '';
        } else if (command === 'help') {
            this.showHelp();
        } else if (command.startsWith('bookmark ')) {
            const url = command.substring(9);
            this.addBookmark(url);
        } else {
            // Default: treat as search query
            this.performSearch(command);
        }
        
        commandInput.value = '';
    }

    navigateToUrl(url) {
        if (!url) return;
        
        // Add protocol if missing
        if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('about:')) {
            url = 'https://' + url;
        }
        
        this.currentUrl = url;
        document.getElementById('url-input').value = url;
        
        // Update browser content
        if (url === 'about:hackerbrowser') {
            document.getElementById('landing-page').classList.add('active');
            document.getElementById('browser-content').style.display = 'none';
        } else {
            document.getElementById('landing-page').classList.remove('active');
            document.getElementById('browser-content').style.display = 'block';
            document.getElementById('browser-content').src = url;
        }
        
        this.history.push(url);
    }

    performSearch(query) {
        const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;
        this.navigateToUrl(searchUrl);
    }

    switchTab(tabName) {
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        this.currentTab = tabName;
        this.loadBookmarkContent();
        
        // Update tab display
        document.querySelector('.tab span').textContent = this.getTabTitle(tabName);
        document.querySelector('.tab i').className = this.getTabIcon(tabName);
    }

    getTabTitle(tabName) {
        const titles = {
            reconnaissance: 'Reconnaissance',
            exploitation: 'Exploitation',
            webapps: 'Web Applications',
            activedirectory: 'Active Directory',
            mobile: 'Mobile Security',
            cloud: 'Cloud Security',
            c2frameworks: 'C2 Frameworks',
            forensics: 'Forensics',
            osint: 'OSINT',
            crypto: 'Cryptography',
            networking: 'Networking',
            malware: 'Malware Analysis',
            social: 'Social Engineering',
            resources: 'Learning Resources'
        };
        return titles[tabName] || 'Unknown';
    }

    getTabIcon(tabName) {
        const icons = {
            reconnaissance: 'fas fa-search',
            exploitation: 'fas fa-bomb',
            webapps: 'fas fa-globe',
            activedirectory: 'fas fa-server',
            mobile: 'fas fa-mobile-alt',
            cloud: 'fas fa-cloud',
            c2frameworks: 'fas fa-satellite-dish',
            forensics: 'fas fa-microscope',
            osint: 'fas fa-eye',
            crypto: 'fas fa-lock',
            networking: 'fas fa-network-wired',
            malware: 'fas fa-virus',
            social: 'fas fa-users',
            resources: 'fas fa-book'
        };
        return icons[tabName] || 'fas fa-globe';
    }

    loadBookmarks() {
        return {
            reconnaissance: [
                { name: 'Nmap', url: 'https://nmap.org/', description: 'Network discovery and security auditing' },
                { name: 'Masscan', url: 'https://github.com/robertdavidgraham/masscan', description: 'Fast port scanner' },
                { name: 'RustScan', url: 'https://github.com/RustScan/RustScan', description: 'Modern port scanner' },
                { name: 'Shodan', url: 'https://www.shodan.io/', description: 'Search engine for Internet-connected devices' },
                { name: 'Censys', url: 'https://censys.io/', description: 'Internet-wide scanning and analysis' },
                { name: 'ZoomEye', url: 'https://www.zoomeye.org/', description: 'Cyberspace search engine' },
                { name: 'Recon-ng', url: 'https://github.com/lanmaster53/recon-ng', description: 'Web reconnaissance framework' },
                { name: 'Amass', url: 'https://github.com/OWASP/Amass', description: 'In-depth attack surface mapping' },
                { name: 'Subfinder', url: 'https://github.com/projectdiscovery/subfinder', description: 'Subdomain discovery tool' },
                { name: 'Assetfinder', url: 'https://github.com/tomnomnom/assetfinder', description: 'Find domains and subdomains' },
                { name: 'DNSRecon', url: 'https://github.com/darkoperator/dnsrecon', description: 'DNS enumeration script' },
                { name: 'Fierce', url: 'https://github.com/mschwager/fierce', description: 'Domain scanner' }
            ],
            exploitation: [
                { name: 'Metasploit', url: 'https://www.metasploit.com/', description: 'Penetration testing framework' },
                { name: 'ExploitDB', url: 'https://www.exploit-db.com/', description: 'Exploit database' },
                { name: 'SearchSploit', url: 'https://github.com/offensive-security/exploitdb', description: 'Local exploit database search' },
                { name: 'Nuclei', url: 'https://github.com/projectdiscovery/nuclei', description: 'Vulnerability scanner based on templates' },
                { name: 'SQLmap', url: 'https://sqlmap.org/', description: 'SQL injection tool' },
                { name: 'XSStrike', url: 'https://github.com/s0md3v/XSStrike', description: 'Advanced XSS detection suite' },
                { name: 'Commix', url: 'https://github.com/commixproject/commix', description: 'Command injection exploiter' },
                { name: 'NoSQLMap', url: 'https://github.com/codingo/NoSQLMap', description: 'NoSQL injection testing tool' },
                { name: 'Ysoserial', url: 'https://github.com/frohoff/ysoserial', description: 'Java deserialization exploit tool' },
                { name: 'Empire', url: 'https://github.com/EmpireProject/Empire', description: 'PowerShell post-exploitation agent' }
            ],
            webapps: [
                { name: 'Burp Suite', url: 'https://portswigger.net/burp', description: 'Web application security testing platform' },
                { name: 'OWASP ZAP', url: 'https://www.zaproxy.org/', description: 'Web application security scanner' },
                { name: 'Nikto', url: 'https://github.com/sullo/nikto', description: 'Web server scanner' },
                { name: 'Gobuster', url: 'https://github.com/OJ/gobuster', description: 'Directory/file & DNS busting tool' },
                { name: 'Ffuf', url: 'https://github.com/ffuf/ffuf', description: 'Fast web fuzzer' },
                { name: 'Dirb', url: 'https://github.com/v0re/dirb', description: 'Web content scanner' },
                { name: 'Wfuzz', url: 'https://github.com/xmendez/wfuzz', description: 'Web application fuzzer' },
                { name: 'Feroxbuster', url: 'https://github.com/epi052/feroxbuster', description: 'Fast, simple, recursive content discovery' },
                { name: 'Arjun', url: 'https://github.com/s0md3v/Arjun', description: 'HTTP parameter discovery suite' },
                { name: 'ParamSpider', url: 'https://github.com/devanshbatham/ParamSpider', description: 'Parameter mining tool' },
                { name: 'Waybackurls', url: 'https://github.com/tomnomnom/waybackurls', description: 'Fetch URLs from Wayback Machine' },
                { name: 'Gau', url: 'https://github.com/lc/gau', description: 'Get All URLs from multiple sources' }
            ],
            activedirectory: [
                { name: 'BloodHound', url: 'https://github.com/BloodHoundAD/BloodHound', description: 'AD attack path analysis' },
                { name: 'Impacket', url: 'https://github.com/SecureAuthCorp/impacket', description: 'Python classes for network protocols' },
                { name: 'Responder', url: 'https://github.com/lgandx/Responder', description: 'LLMNR, NBT-NS and MDNS poisoner' },
                { name: 'Kerbrute', url: 'https://github.com/ropnop/kerbrute', description: 'Kerberos username enumeration' },
                { name: 'CrackMapExec', url: 'https://github.com/byt3bl33d3r/CrackMapExec', description: 'Swiss army knife for pentesting networks' },
                { name: 'Mimikatz', url: 'https://github.com/gentilkiwi/mimikatz', description: 'Extract plaintexts passwords, hash, PIN code' },
                { name: 'Rubeus', url: 'https://github.com/GhostPack/Rubeus', description: 'Kerberos interaction and abuses' },
                { name: 'PowerView', url: 'https://github.com/PowerShellMafia/PowerSploit', description: 'PowerShell tool for AD enumeration' },
                { name: 'ADRecon', url: 'https://github.com/adrecon/ADRecon', description: 'AD enumeration tool' },
                { name: 'PingCastle', url: 'https://www.pingcastle.com/', description: 'AD security assessment tool' },
                { name: 'SharpHound', url: 'https://github.com/BloodHoundAD/SharpHound', description: 'C# data collector for BloodHound' },
                { name: 'LinWinPwn', url: 'https://github.com/lefayjey/linWinPwn', description: 'AD pentesting from Linux' }
            ],
            mobile: [
                { name: 'MobSF', url: 'https://github.com/MobSF/Mobile-Security-Framework-MobSF', description: 'Mobile security testing framework' },
                { name: 'Frida', url: 'https://frida.re/', description: 'Dynamic instrumentation toolkit' },
                { name: 'Objection', url: 'https://github.com/sensepost/objection', description: 'Runtime mobile exploration' },
                { name: 'Drozer', url: 'https://github.com/FSecureLABS/drozer', description: 'Android security testing framework' },
                { name: 'QARK', url: 'https://github.com/linkedin/qark', description: 'Quick Android Review Kit' },
                { name: 'AndroBugs', url: 'https://github.com/AndroBugs/AndroBugs_Framework', description: 'Android vulnerability scanner' },
                { name: 'iMazing', url: 'https://imazing.com/', description: 'iOS device management' },
                { name: 'Checkra1n', url: 'https://checkra.in/', description: 'iOS jailbreak tool' },
                { name: 'Needle', url: 'https://github.com/FSecureLABS/needle', description: 'iOS security testing framework' },
                { name: 'iProxy', url: 'https://github.com/tcurdt/iProxy', description: 'iOS HTTP proxy' }
            ],
            cloud: [
                { name: 'ScoutSuite', url: 'https://github.com/nccgroup/ScoutSuite', description: 'Multi-cloud security auditing tool' },
                { name: 'Prowler', url: 'https://github.com/prowler-cloud/prowler', description: 'AWS/Azure/GCP security assessment' },
                { name: 'CloudMapper', url: 'https://github.com/duo-labs/cloudmapper', description: 'AWS environment analysis' },
                { name: 'Pacu', url: 'https://github.com/RhinoSecurityLabs/pacu', description: 'AWS exploitation framework' },
                { name: 'CloudGoat', url: 'https://github.com/RhinoSecurityLabs/cloudgoat', description: 'Vulnerable AWS environment' },
                { name: 'AWS CLI', url: 'https://aws.amazon.com/cli/', description: 'AWS command line interface' },
                { name: 'Azure CLI', url: 'https://docs.microsoft.com/en-us/cli/azure/', description: 'Azure command line interface' },
                { name: 'GCloud CLI', url: 'https://cloud.google.com/sdk/gcloud', description: 'Google Cloud command line interface' },
                { name: 'CloudFox', url: 'https://github.com/BishopFox/cloudfox', description: 'AWS situational awareness tool' },
                { name: 'Enumerate-IAM', url: 'https://github.com/andresriancho/enumerate-iam', description: 'AWS IAM enumeration' }
            ],
            c2frameworks: [
                { name: 'Cobalt Strike', url: 'https://www.cobaltstrike.com/', description: 'Commercial adversary simulation platform' },
                { name: 'Sliver', url: 'https://github.com/BishopFox/sliver', description: 'Open source C2 framework' },
                { name: 'Covenant', url: 'https://github.com/cobbr/Covenant', description: '.NET command and control framework' },
                { name: 'Empire', url: 'https://github.com/BC-SECURITY/Empire', description: 'PowerShell and Python post-exploitation agent' },
                { name: 'Mythic', url: 'https://github.com/its-a-feature/Mythic', description: 'Cross-platform C2 framework' },
                { name: 'Havoc', url: 'https://github.com/HavocFramework/Havoc', description: 'Modern and malleable C2 framework' },
                { name: 'Merlin', url: 'https://github.com/Ne0nd0g/merlin', description: 'Cross-platform post-exploitation HTTP/2 C2' },
                { name: 'PoshC2', url: 'https://github.com/nettitude/PoshC2', description: 'Proxy aware C2 framework' },
                { name: 'Koadic', url: 'https://github.com/zerosum0x0/koadic', description: 'Windows post-exploitation rootkit' },
                { name: 'Silver', url: 'https://github.com/BishopFox/sliver', description: 'Adversary emulation framework' }
            ],
            forensics: [
                { name: 'Autopsy', url: 'https://www.autopsy.com/', description: 'Digital forensics platform' },
                { name: 'Volatility', url: 'https://www.volatilityfoundation.org/', description: 'Memory forensics framework' },
                { name: 'Sleuth Kit', url: 'https://www.sleuthkit.org/', description: 'Digital investigation tools' },
                { name: 'YARA', url: 'https://virustotal.github.io/yara/', description: 'Malware identification and classification' },
                { name: 'Wireshark', url: 'https://www.wireshark.org/', description: 'Network protocol analyzer' }
            ],
            osint: [
                { name: 'Maltego', url: 'https://www.maltego.com/', description: 'Link analysis and data mining' },
                { name: 'theHarvester', url: 'https://github.com/laramies/theHarvester', description: 'Email, subdomain and people names harvester' },
                { name: 'SpiderFoot', url: 'https://www.spiderfoot.net/', description: 'OSINT automation tool' },
                { name: 'Sherlock', url: 'https://github.com/sherlock-project/sherlock', description: 'Hunt down social media accounts' },
                { name: 'Recon-ng', url: 'https://github.com/lanmaster53/recon-ng', description: 'Web reconnaissance framework' },
                { name: 'OSINT Framework', url: 'https://osintframework.com/', description: 'Collection of OSINT tools' },
                { name: 'Have I Been Pwned', url: 'https://haveibeenpwned.com/', description: 'Check if email has been compromised' },
                { name: 'Twint', url: 'https://github.com/twintproject/twint', description: 'Twitter intelligence tool' },
                { name: 'Social Analyzer', url: 'https://github.com/qeeqbox/social-analyzer', description: 'Social media analysis tool' },
                { name: 'Photon', url: 'https://github.com/s0md3v/Photon', description: 'Fast web crawler for OSINT' },
                { name: 'InSpy', url: 'https://github.com/leapsecurity/InSpy', description: 'LinkedIn enumeration tool' },
                { name: 'Metagoofil', url: 'https://github.com/laramies/metagoofil', description: 'Metadata harvester' },
                { name: 'Creepy', url: 'https://github.com/ilektrojohn/creepy', description: 'Geolocation OSINT tool' },
                { name: 'Tinfoleak', url: 'https://github.com/vaguileradiaz/tinfoleak', description: 'Twitter intelligence analysis' },
                { name: 'Datasploit', url: 'https://github.com/DataSploit/datasploit', description: 'OSINT framework' }
            ],
            crypto: [
                { name: 'Hashcat', url: 'https://hashcat.net/hashcat/', description: 'Advanced password recovery' },
                { name: 'John the Ripper', url: 'https://www.openwall.com/john/', description: 'Password cracking tool' },
                { name: 'CyberChef', url: 'https://gchq.github.io/CyberChef/', description: 'Cyber Swiss Army Knife' },
                { name: 'Hash Analyzer', url: 'https://www.tunnelsup.com/hash-analyzer/', description: 'Hash identification tool' },
                { name: 'CrackStation', url: 'https://crackstation.net/', description: 'Online hash cracking' }
            ],
            networking: [
                { name: 'Nessus', url: 'https://www.tenable.com/products/nessus', description: 'Vulnerability scanner' },
                { name: 'OpenVAS', url: 'https://www.openvas.org/', description: 'Open source vulnerability scanner' },
                { name: 'Aircrack-ng', url: 'https://www.aircrack-ng.org/', description: 'WiFi security auditing tools' },
                { name: 'Kismet', url: 'https://www.kismetwireless.net/', description: 'Wireless network detector' },
                { name: 'Netcat', url: 'https://nc110.sourceforge.io/', description: 'Network utility for reading/writing network connections' }
            ],
            malware: [
                { name: 'VirusTotal', url: 'https://www.virustotal.com/', description: 'Malware analysis service' },
                { name: 'Hybrid Analysis', url: 'https://www.hybrid-analysis.com/', description: 'Free malware analysis service' },
                { name: 'Cuckoo Sandbox', url: 'https://cuckoosandbox.org/', description: 'Automated malware analysis' },
                { name: 'YARA Rules', url: 'https://github.com/Yara-Rules/rules', description: 'YARA rules repository' },
                { name: 'Malware Bazaar', url: 'https://bazaar.abuse.ch/', description: 'Malware sample sharing' }
            ],
            social: [
                { name: 'SET Toolkit', url: 'https://github.com/trustedsec/social-engineer-toolkit', description: 'Social engineering toolkit' },
                { name: 'Gophish', url: 'https://getgophish.com/', description: 'Phishing framework' },
                { name: 'BeEF', url: 'https://beefproject.com/', description: 'Browser exploitation framework' },
                { name: 'King Phisher', url: 'https://github.com/securestate/king-phisher', description: 'Phishing campaign toolkit' },
                { name: 'Social Mapper', url: 'https://github.com/Greenwolf/social_mapper', description: 'Social media enumeration tool' },
                { name: 'Evilginx2', url: 'https://github.com/kgretzky/evilginx2', description: 'Advanced phishing framework' },
                { name: 'Modlishka', url: 'https://github.com/drk1wi/Modlishka', description: 'Reverse proxy phishing tool' },
                { name: 'SocialFish', url: 'https://github.com/UndeadSec/SocialFish', description: 'Educational phishing tool' },
                { name: 'Blackeye', url: 'https://github.com/An0nUD4Y/blackeye', description: 'Phishing pages generator' }
            ],
            resources: [
                { name: 'OWASP Top 10', url: 'https://owasp.org/www-project-top-ten/', description: 'Top 10 web application security risks' },
                { name: 'NIST Cybersecurity Framework', url: 'https://www.nist.gov/cyberframework', description: 'Cybersecurity framework' },
                { name: 'SANS Reading Room', url: 'https://www.sans.org/reading-room/', description: 'Security research papers' },
                { name: 'Krebs on Security', url: 'https://krebsonsecurity.com/', description: 'Security news and investigation' },
                { name: 'Dark Reading', url: 'https://www.darkreading.com/', description: 'Cybersecurity news and analysis' },
                { name: 'The Hacker News', url: 'https://thehackernews.com/', description: 'Cybersecurity news' },
                { name: 'Phrack Magazine', url: 'http://phrack.org/', description: 'Underground hacking magazine' },
                { name: '2600 Magazine', url: 'https://www.2600.com/', description: 'Hacker quarterly magazine' },
                { name: 'Pentester Academy', url: 'https://www.pentesteracademy.com/', description: 'Hands-on security courses' },
                { name: 'Cybrary', url: 'https://www.cybrary.it/', description: 'Free cybersecurity training' },
                { name: 'VulnHub', url: 'https://www.vulnhub.com/', description: 'Vulnerable VMs for practice' },
                { name: 'HackTheBox', url: 'https://www.hackthebox.eu/', description: 'Online penetration testing labs' },
                { name: 'TryHackMe', url: 'https://tryhackme.com/', description: 'Learn cybersecurity through hands-on exercises' },
                { name: 'OverTheWire', url: 'https://overthewire.org/', description: 'Wargames and security challenges' },
                { name: 'PentesterLab', url: 'https://pentesterlab.com/', description: 'Web application security exercises' }
            ]
        };
    }

    loadBookmarkContent() {
        const bookmarkContent = document.getElementById('bookmark-content');
        const bookmarks = this.bookmarks[this.currentTab] || [];
        
        bookmarkContent.innerHTML = `
            <div class="bookmark-category">
                <h4><i class="${this.getTabIcon(this.currentTab)}"></i> ${this.getTabTitle(this.currentTab)}</h4>
                <div class="bookmark-list">
                    ${bookmarks.map(bookmark => `
                        <div class="bookmark-item" data-url="${bookmark.url}">
                            <div class="bookmark-name">${bookmark.name}</div>
                            <div class="bookmark-desc">${bookmark.description}</div>
                            <div class="bookmark-url">${bookmark.url}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Add click listeners to bookmarks
        document.querySelectorAll('.bookmark-item').forEach(item => {
            item.addEventListener('click', () => {
                this.navigateToUrl(item.dataset.url);
            });
        });
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }

    simulateSystemStats() {
        // Simulate realistic system stats
        const memoryUsage = (Math.random() * 2 + 1.5).toFixed(1);
        const cpuUsage = Math.floor(Math.random() * 30 + 10);
        const diskUsage = Math.floor(Math.random() * 20 + 40);

        document.getElementById('memory-usage').textContent = `RAM: ${memoryUsage}GB`;
        document.getElementById('cpu-usage').textContent = `CPU: ${cpuUsage}%`;
        document.getElementById('disk-usage').textContent = `DISK: ${diskUsage}%`;

        // Simulate IP address changes (VPN switching)
        const ips = ['*************', '*********', '***********', '************'];
        if (Math.random() > 0.95) { // 5% chance to change IP
            const randomIP = ips[Math.floor(Math.random() * ips.length)];
            document.getElementById('ip-address').textContent = `IP: ${randomIP}`;
        }
    }

    initMatrixEffect() {
        // Add subtle matrix-style background animation
        const matrixBg = document.querySelector('.matrix-bg');
        setInterval(() => {
            const opacity = Math.random() * 0.1 + 0.02;
            matrixBg.style.background = `linear-gradient(45deg, 
                rgba(0, 255, 65, ${opacity}) 0%, 
                rgba(0, 0, 0, 0.95) 50%, 
                rgba(0, 255, 255, ${opacity}) 100%)`;
        }, 2000);
    }

    toggleResourcePanel() {
        const panel = document.querySelector('.resource-panel');
        const toggle = document.querySelector('.panel-toggle i');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'flex';
            toggle.className = 'fas fa-chevron-right';
        } else {
            panel.style.display = 'none';
            toggle.className = 'fas fa-chevron-left';
        }
    }

    showHelp() {
        alert(`HackerBrowser v2.1.0 Commands:
        
• Enter URL or domain to navigate
• search <query> - Search the web
• bookmark <url> - Add bookmark
• clear - Clear command input
• help - Show this help

Navigation:
• Use sidebar to switch between tool categories
• Click bookmarks to navigate
• Use browser controls for back/forward/refresh`);
    }

    goBack() {
        if (this.history.length > 1) {
            this.history.pop(); // Remove current
            const previousUrl = this.history[this.history.length - 1];
            this.navigateToUrl(previousUrl);
        }
    }

    goForward() {
        // Implement forward functionality
        console.log('Forward navigation');
    }

    refresh() {
        this.navigateToUrl(this.currentUrl);
    }

    addBookmark(url) {
        console.log(`Bookmark added: ${url}`);
        // Implement bookmark addition
    }

    // System Tools Functions
    openTerminal() {
        document.getElementById('terminal-modal').style.display = 'block';
        document.getElementById('terminal-input').focus();
    }

    openNotes() {
        document.getElementById('notes-modal').style.display = 'block';
        // Load saved notes
        const savedNotes = localStorage.getItem('hackerNotes') || '';
        document.getElementById('notes-textarea').value = savedNotes;
    }

    openScanner() {
        document.getElementById('scanner-modal').style.display = 'block';
    }

    openEncoder() {
        document.getElementById('encoder-modal').style.display = 'block';
    }

    executeTerminalCommand(command) {
        const output = document.getElementById('terminal-output');
        const commandLine = document.createElement('div');
        commandLine.className = 'terminal-line';
        commandLine.innerHTML = `
            <span class="terminal-prompt">root@hackbox:~#</span>
            <span class="terminal-text">${command}</span>
        `;
        output.appendChild(commandLine);

        // Process command
        let response = '';
        const cmd = command.toLowerCase().trim();

        if (cmd === 'help') {
            response = `Available commands:
help - Show this help
clear - Clear terminal
whoami - Show current user
pwd - Show current directory
ls - List directory contents
nmap - Network mapper (simulation)
ping - Ping a host (simulation)
netstat - Show network connections
ps - Show running processes
uname - System information`;
        } else if (cmd === 'clear') {
            output.innerHTML = '';
            return;
        } else if (cmd === 'whoami') {
            response = 'root';
        } else if (cmd === 'pwd') {
            response = '/root';
        } else if (cmd === 'ls') {
            response = 'Desktop  Documents  Downloads  Pictures  Scripts  Tools';
        } else if (cmd.startsWith('nmap')) {
            response = 'Starting Nmap scan...\nHost is up (0.001s latency)\nPorts: 22/tcp open ssh, 80/tcp open http, 443/tcp open https';
        } else if (cmd.startsWith('ping')) {
            response = 'PING target: 64 bytes from target: icmp_seq=1 ttl=64 time=1.2 ms';
        } else if (cmd === 'netstat') {
            response = 'Active connections:\ntcp 0.0.0.0:22 LISTEN\ntcp 0.0.0.0:80 LISTEN\ntcp 0.0.0.0:443 LISTEN';
        } else if (cmd === 'ps') {
            response = 'PID  COMMAND\n1234 hackerbrowser\n5678 terminal\n9012 scanner';
        } else if (cmd === 'uname') {
            response = 'Linux hackbox 5.15.0-kali #1 SMP x86_64 GNU/Linux';
        } else {
            response = `Command not found: ${command}`;
        }

        const responseLine = document.createElement('div');
        responseLine.className = 'terminal-line';
        responseLine.innerHTML = `<span class="terminal-text">${response}</span>`;
        output.appendChild(responseLine);

        output.scrollTop = output.scrollHeight;
    }

    saveNotes() {
        const notes = document.getElementById('notes-textarea').value;
        localStorage.setItem('hackerNotes', notes);
        this.showNotification('Notes saved successfully!');
    }

    clearNotes() {
        if (confirm('Are you sure you want to clear all notes?')) {
            document.getElementById('notes-textarea').value = '';
            localStorage.removeItem('hackerNotes');
            this.showNotification('Notes cleared!');
        }
    }

    performPortScan() {
        const target = document.getElementById('target-input').value;
        const ports = document.getElementById('ports-input').value;
        const output = document.getElementById('scanner-output');

        if (!target) {
            output.innerHTML = '<div class="scan-result">Please enter a target IP or domain</div>';
            return;
        }

        output.innerHTML = '<div class="scan-result">Scanning target: ' + target + '</div>';

        // Simulate port scanning
        setTimeout(() => {
            const commonPorts = [22, 23, 25, 53, 80, 110, 143, 443, 993, 995];
            let results = '<div class="scan-result">Scan completed for ' + target + '</div>';

            commonPorts.forEach(port => {
                const isOpen = Math.random() > 0.7; // 30% chance port is open
                const status = isOpen ? 'open' : 'closed';
                const service = this.getServiceName(port);
                results += `<div class="scan-result ${status}">Port ${port}/tcp ${status} ${service}</div>`;
            });

            output.innerHTML = results;
        }, 2000);
    }

    getServiceName(port) {
        const services = {
            22: 'ssh', 23: 'telnet', 25: 'smtp', 53: 'dns',
            80: 'http', 110: 'pop3', 143: 'imap', 443: 'https',
            993: 'imaps', 995: 'pop3s'
        };
        return services[port] || 'unknown';
    }

    encodeText() {
        const input = document.getElementById('input-text').value;
        const type = document.getElementById('encoding-type').value;
        const output = document.getElementById('output-text');

        if (!input) {
            output.value = 'Please enter text to encode';
            return;
        }

        try {
            let result = '';
            switch (type) {
                case 'base64':
                    result = btoa(input);
                    break;
                case 'url':
                    result = encodeURIComponent(input);
                    break;
                case 'html':
                    result = input.replace(/[&<>"']/g, (m) => ({
                        '&': '&amp;', '<': '&lt;', '>': '&gt;',
                        '"': '&quot;', "'": '&#39;'
                    }[m]));
                    break;
                case 'hex':
                    result = Array.from(input).map(c => c.charCodeAt(0).toString(16).padStart(2, '0')).join('');
                    break;
                case 'md5':
                case 'sha1':
                case 'sha256':
                    result = 'Hash functions require crypto library (not available in browser)';
                    break;
                default:
                    result = 'Encoding type not supported';
            }
            output.value = result;
        } catch (error) {
            output.value = 'Encoding error: ' + error.message;
        }
    }

    decodeText() {
        const input = document.getElementById('input-text').value;
        const type = document.getElementById('encoding-type').value;
        const output = document.getElementById('output-text');

        if (!input) {
            output.value = 'Please enter text to decode';
            return;
        }

        try {
            let result = '';
            switch (type) {
                case 'base64':
                    result = atob(input);
                    break;
                case 'url':
                    result = decodeURIComponent(input);
                    break;
                case 'html':
                    const textarea = document.createElement('textarea');
                    textarea.innerHTML = input;
                    result = textarea.value;
                    break;
                case 'hex':
                    result = input.match(/.{1,2}/g).map(byte => String.fromCharCode(parseInt(byte, 16))).join('');
                    break;
                default:
                    result = 'Decoding type not supported or not applicable';
            }
            output.value = result;
        } catch (error) {
            output.value = 'Decoding error: ' + error.message;
        }
    }

    showNotification(message) {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-green);
            color: var(--terminal-bg);
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-family: 'Fira Code', monospace;
            box-shadow: var(--glow-green);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the hacker browser when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HackerBrowser();
});
