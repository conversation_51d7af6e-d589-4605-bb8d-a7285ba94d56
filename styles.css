/* Hacker Browser - Cyberpunk Terminal Theme */

:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --terminal-bg: #000000;
    --accent-green: #00ff41;
    --accent-cyan: #00ffff;
    --accent-red: #ff0040;
    --accent-yellow: #ffff00;
    --text-primary: #00ff41;
    --text-secondary: #00ffff;
    --text-muted: #666666;
    --border-color: #333333;
    --glow-green: 0 0 10px #00ff41;
    --glow-cyan: 0 0 10px #00ffff;
    --glow-red: 0 0 10px #ff0040;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Fira Code', 'Share Tech Mono', monospace;
    background: var(--primary-bg);
    color: var(--text-primary);
    overflow-x: hidden;
    position: relative;
}

/* Matrix Background Effect */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(0, 255, 65, 0.03) 0%, 
        rgba(0, 0, 0, 0.95) 50%, 
        rgba(0, 255, 255, 0.03) 100%);
    z-index: -1;
    animation: matrixFlow 20s linear infinite;
}

@keyframes matrixFlow {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* Terminal Header */
.terminal-header {
    background: var(--terminal-bg);
    border-bottom: 2px solid var(--accent-green);
    box-shadow: var(--glow-green);
}

.terminal-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    height: 40px;
}

.terminal-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
}

.control.close { background: var(--accent-red); }
.control.minimize { background: var(--accent-yellow); }
.control.maximize { background: var(--accent-green); }

.terminal-title {
    font-weight: 500;
    color: var(--text-primary);
    text-shadow: var(--glow-green);
}

.system-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    color: var(--text-secondary);
}

.status-indicator {
    color: var(--accent-green);
    text-shadow: var(--glow-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Command Interface */
.command-interface {
    background: var(--secondary-bg);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.command-prompt {
    display: flex;
    align-items: center;
    gap: 10px;
}

.prompt-symbol {
    color: var(--accent-green);
    font-weight: bold;
    text-shadow: var(--glow-green);
}

#command-input {
    flex: 1;
    background: var(--terminal-bg);
    border: 1px solid var(--accent-green);
    color: var(--text-primary);
    padding: 8px 12px;
    font-family: inherit;
    font-size: 14px;
    border-radius: 4px;
    outline: none;
    transition: all 0.3s ease;
}

#command-input:focus {
    box-shadow: var(--glow-green);
    border-color: var(--accent-cyan);
}

#execute-btn {
    background: var(--accent-green);
    color: var(--terminal-bg);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#execute-btn:hover {
    background: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
}

/* Main Container */
.main-container {
    display: flex;
    height: calc(100vh - 120px);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    padding: 20px;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section h3 {
    color: var(--accent-cyan);
    font-size: 14px;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: var(--glow-cyan);
}

.nav-menu, .quick-links {
    list-style: none;
}

.nav-item {
    padding: 12px 16px;
    margin-bottom: 5px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(0, 255, 65, 0.1);
    border-left-color: var(--accent-green);
    box-shadow: inset 5px 0 10px rgba(0, 255, 65, 0.2);
}

.nav-item.active {
    background: rgba(0, 255, 65, 0.2);
    border-left-color: var(--accent-green);
    color: var(--accent-green);
    text-shadow: var(--glow-green);
}

.nav-item i {
    margin-right: 10px;
    width: 16px;
}

.quick-links li {
    margin-bottom: 8px;
}

.quick-links a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 8px 12px;
    display: block;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.quick-links a:hover {
    color: var(--accent-cyan);
    background: rgba(0, 255, 255, 0.1);
    text-shadow: var(--glow-cyan);
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.browser-frame {
    flex: 1;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    margin: 10px;
    border-radius: 8px;
    overflow: hidden;
}

.browser-header {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--terminal-bg);
    border-bottom: 1px solid var(--border-color);
    gap: 10px;
}

.browser-controls {
    display: flex;
    gap: 5px;
}

.browser-btn {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.browser-btn:hover {
    background: var(--accent-green);
    color: var(--terminal-bg);
    box-shadow: var(--glow-green);
}

.address-bar {
    flex: 1;
    display: flex;
    gap: 5px;
}

#url-input {
    flex: 1;
    background: var(--terminal-bg);
    border: 1px solid var(--accent-green);
    color: var(--text-primary);
    padding: 6px 12px;
    font-family: inherit;
    border-radius: 4px;
    outline: none;
}

#url-input:focus {
    box-shadow: var(--glow-green);
}

/* Tab Container */
.tab-container {
    display: flex;
    background: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0 10px;
}

.tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: var(--terminal-bg);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    max-width: 200px;
}

.tab.active {
    background: var(--secondary-bg);
    border-color: var(--accent-green);
    color: var(--accent-green);
    text-shadow: var(--glow-green);
}

.tab-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.tab-close:hover {
    color: var(--accent-red);
    background: rgba(255, 0, 64, 0.2);
}

.new-tab-btn {
    background: var(--terminal-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin: 5px;
    transition: all 0.3s ease;
}

.new-tab-btn:hover {
    background: var(--accent-green);
    color: var(--terminal-bg);
    box-shadow: var(--glow-green);
}

/* Content Frame */
.content-frame {
    flex: 1;
    position: relative;
    background: var(--terminal-bg);
}

#browser-content {
    width: 100%;
    height: 100%;
    border: none;
    background: var(--terminal-bg);
}

/* Landing Page */
.landing-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--terminal-bg);
    display: none;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px;
}

.landing-page.active {
    display: flex;
}

.welcome-section h1 {
    color: var(--accent-green);
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: var(--glow-green);
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.2em;
    margin-bottom: 30px;
}

.ascii-art {
    color: var(--accent-cyan);
    font-size: 10px;
    line-height: 1;
    text-shadow: var(--glow-cyan);
    animation: flicker 3s infinite;
}

@keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Resource Panel */
.resource-panel {
    width: 320px;
    background: var(--secondary-bg);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: var(--terminal-bg);
    border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
    color: var(--accent-cyan);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: var(--glow-cyan);
}

.panel-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.panel-toggle:hover {
    color: var(--accent-cyan);
    background: rgba(0, 255, 255, 0.1);
}

/* Status Bar */
.status-bar {
    height: 30px;
    background: var(--terminal-bg);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    font-size: 12px;
}

.status-left, .status-right {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-secondary);
}

.status-item i {
    color: var(--accent-green);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--terminal-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-green);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-cyan);
}

/* Bookmark Sections */
.bookmark-sections {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.bookmark-category h4 {
    color: var(--accent-cyan);
    font-size: 14px;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: var(--glow-cyan);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.bookmark-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.bookmark-item {
    background: var(--terminal-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.bookmark-item:hover {
    border-left-color: var(--accent-green);
    background: rgba(0, 255, 65, 0.05);
    box-shadow: 0 2px 8px rgba(0, 255, 65, 0.2);
    transform: translateX(5px);
}

.bookmark-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
}

.bookmark-desc {
    color: var(--text-muted);
    font-size: 12px;
    margin-bottom: 6px;
    line-height: 1.4;
}

.bookmark-url {
    color: var(--accent-cyan);
    font-size: 11px;
    font-family: 'Fira Code', monospace;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sidebar {
        width: 240px;
    }

    .resource-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar, .resource-panel {
        width: 100%;
        height: auto;
    }

    .ascii-art {
        font-size: 8px;
    }

    .welcome-section h1 {
        font-size: 2em;
    }
}
