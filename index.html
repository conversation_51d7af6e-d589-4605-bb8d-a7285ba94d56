<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HackerBrowser v2.1.0 - Terminal Interface</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="matrix-bg"></div>
    
    <!-- Header Terminal -->
    <header class="terminal-header">
        <div class="terminal-bar">
            <div class="terminal-controls">
                <span class="control close"></span>
                <span class="control minimize"></span>
                <span class="control maximize"></span>
            </div>
            <div class="terminal-title">
                <i class="fas fa-terminal"></i>
                HackerBrowser v2.1.0 - [root@hackbox:~]
            </div>
            <div class="system-info">
                <span id="current-time"></span>
                <span class="separator">|</span>
                <span class="status-indicator">ONLINE</span>
            </div>
        </div>
    </header>

    <!-- Command Interface -->
    <div class="command-interface">
        <div class="command-prompt">
            <span class="prompt-symbol">root@hackbox:~$</span>
            <input type="text" id="command-input" placeholder="Enter command or URL..." autocomplete="off">
            <button id="execute-btn"><i class="fas fa-play"></i></button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <h3><i class="fas fa-skull"></i> HACKER ARSENAL</h3>
                <ul class="nav-menu">
                    <li class="nav-item active" data-tab="reconnaissance">
                        <i class="fas fa-search"></i> Reconnaissance
                    </li>
                    <li class="nav-item" data-tab="exploitation">
                        <i class="fas fa-bomb"></i> Exploitation
                    </li>
                    <li class="nav-item" data-tab="forensics">
                        <i class="fas fa-microscope"></i> Forensics
                    </li>
                    <li class="nav-item" data-tab="osint">
                        <i class="fas fa-eye"></i> OSINT
                    </li>
                    <li class="nav-item" data-tab="crypto">
                        <i class="fas fa-lock"></i> Cryptography
                    </li>
                    <li class="nav-item" data-tab="networking">
                        <i class="fas fa-network-wired"></i> Networking
                    </li>
                    <li class="nav-item" data-tab="malware">
                        <i class="fas fa-virus"></i> Malware Analysis
                    </li>
                    <li class="nav-item" data-tab="social">
                        <i class="fas fa-users"></i> Social Engineering
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3><i class="fas fa-server"></i> QUICK ACCESS</h3>
                <ul class="quick-links">
                    <li><a href="#" data-url="https://www.kali.org/tools/"><i class="fas fa-tools"></i> Kali Tools</a></li>
                    <li><a href="#" data-url="https://github.com/"><i class="fab fa-github"></i> GitHub</a></li>
                    <li><a href="#" data-url="https://exploit-db.com/"><i class="fas fa-database"></i> ExploitDB</a></li>
                    <li><a href="#" data-url="https://cve.mitre.org/"><i class="fas fa-shield-alt"></i> CVE Database</a></li>
                </ul>
            </div>
        </nav>

        <!-- Content Area -->
        <main class="content-area">
            <!-- Browser Frame -->
            <div class="browser-frame">
                <div class="browser-header">
                    <div class="browser-controls">
                        <button class="browser-btn" id="back-btn"><i class="fas fa-arrow-left"></i></button>
                        <button class="browser-btn" id="forward-btn"><i class="fas fa-arrow-right"></i></button>
                        <button class="browser-btn" id="refresh-btn"><i class="fas fa-redo"></i></button>
                    </div>
                    <div class="address-bar">
                        <input type="url" id="url-input" placeholder="https://target.com" value="about:hackerbrowser">
                        <button id="go-btn"><i class="fas fa-arrow-right"></i></button>
                    </div>
                    <div class="browser-actions">
                        <button class="browser-btn" id="bookmark-btn"><i class="fas fa-bookmark"></i></button>
                        <button class="browser-btn" id="tools-btn"><i class="fas fa-cog"></i></button>
                    </div>
                </div>
                
                <!-- Tab Container -->
                <div class="tab-container">
                    <div class="tab active" data-content="reconnaissance">
                        <i class="fas fa-search"></i>
                        <span>Reconnaissance</span>
                        <button class="tab-close"><i class="fas fa-times"></i></button>
                    </div>
                    <button class="new-tab-btn"><i class="fas fa-plus"></i></button>
                </div>

                <!-- Content Frame -->
                <div class="content-frame">
                    <iframe id="browser-content" src="about:blank"></iframe>
                    
                    <!-- Default Landing Page -->
                    <div id="landing-page" class="landing-page active">
                        <div class="welcome-section">
                            <h1><i class="fas fa-skull"></i> HACKERBROWSER v2.1.0</h1>
                            <p class="subtitle">Your Gateway to the Digital Underground</p>
                            <div class="ascii-art">
                                <pre>
    ██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗ 
    ██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
    ███████║███████║██║     █████╔╝ █████╗  ██████╔╝
    ██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
    ██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
    ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
                                                    
                    BROWSER v2.1.0 - TERMINAL MODE
                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Resource Panel -->
        <aside class="resource-panel">
            <div class="panel-header">
                <h3><i class="fas fa-bookmark"></i> BOOKMARKS</h3>
                <button class="panel-toggle"><i class="fas fa-chevron-right"></i></button>
            </div>
            
            <div class="bookmark-sections" id="bookmark-content">
                <!-- Bookmark content will be loaded here -->
            </div>
        </aside>
    </div>

    <!-- Footer Status Bar -->
    <footer class="status-bar">
        <div class="status-left">
            <span class="status-item">
                <i class="fas fa-wifi"></i>
                <span id="connection-status">CONNECTED</span>
            </span>
            <span class="status-item">
                <i class="fas fa-shield-alt"></i>
                <span>VPN: ACTIVE</span>
            </span>
        </div>
        <div class="status-right">
            <span class="status-item">
                <i class="fas fa-memory"></i>
                <span id="memory-usage">RAM: 2.1GB</span>
            </span>
            <span class="status-item">
                <i class="fas fa-microchip"></i>
                <span id="cpu-usage">CPU: 15%</span>
            </span>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
