<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HackerBrowser v2.1.0 - Terminal Interface</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="matrix-bg"></div>
    
    <!-- Header Terminal -->
    <header class="terminal-header">
        <div class="terminal-bar">
            <div class="terminal-controls">
                <span class="control close"></span>
                <span class="control minimize"></span>
                <span class="control maximize"></span>
            </div>
            <div class="terminal-title">
                <i class="fas fa-terminal"></i>
                HackerBrowser v2.1.0 - [root@hackbox:~]
            </div>
            <div class="system-info">
                <span id="current-time"></span>
                <span class="separator">|</span>
                <span class="status-indicator">ONLINE</span>
            </div>
        </div>
    </header>

    <!-- Command Interface -->
    <div class="command-interface">
        <div class="command-prompt">
            <span class="prompt-symbol">root@hackbox:~$</span>
            <input type="text" id="command-input" placeholder="Enter command or URL..." autocomplete="off">
            <button id="execute-btn"><i class="fas fa-play"></i></button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <h3><i class="fas fa-skull"></i> HACKER ARSENAL</h3>
                <ul class="nav-menu">
                    <li class="nav-item active" data-tab="reconnaissance">
                        <i class="fas fa-search"></i> Reconnaissance
                    </li>
                    <li class="nav-item" data-tab="exploitation">
                        <i class="fas fa-bomb"></i> Exploitation
                    </li>
                    <li class="nav-item" data-tab="webapps">
                        <i class="fas fa-globe"></i> Web Applications
                    </li>
                    <li class="nav-item" data-tab="activedirectory">
                        <i class="fas fa-server"></i> Active Directory
                    </li>
                    <li class="nav-item" data-tab="mobile">
                        <i class="fas fa-mobile-alt"></i> Mobile Security
                    </li>
                    <li class="nav-item" data-tab="cloud">
                        <i class="fas fa-cloud"></i> Cloud Security
                    </li>
                    <li class="nav-item" data-tab="c2frameworks">
                        <i class="fas fa-satellite-dish"></i> C2 Frameworks
                    </li>
                    <li class="nav-item" data-tab="forensics">
                        <i class="fas fa-microscope"></i> Forensics
                    </li>
                    <li class="nav-item" data-tab="osint">
                        <i class="fas fa-eye"></i> OSINT
                    </li>
                    <li class="nav-item" data-tab="crypto">
                        <i class="fas fa-lock"></i> Cryptography
                    </li>
                    <li class="nav-item" data-tab="networking">
                        <i class="fas fa-network-wired"></i> Networking
                    </li>
                    <li class="nav-item" data-tab="malware">
                        <i class="fas fa-virus"></i> Malware Analysis
                    </li>
                    <li class="nav-item" data-tab="social">
                        <i class="fas fa-users"></i> Social Engineering
                    </li>
                    <li class="nav-item" data-tab="resources">
                        <i class="fas fa-book"></i> Learning Resources
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3><i class="fas fa-server"></i> QUICK ACCESS</h3>
                <ul class="quick-links">
                    <li><a href="#" data-url="https://www.kali.org/tools/"><i class="fas fa-tools"></i> Kali Tools</a></li>
                    <li><a href="#" data-url="https://github.com/"><i class="fab fa-github"></i> GitHub</a></li>
                    <li><a href="#" data-url="https://exploit-db.com/"><i class="fas fa-database"></i> ExploitDB</a></li>
                    <li><a href="#" data-url="https://cve.mitre.org/"><i class="fas fa-shield-alt"></i> CVE Database</a></li>
                    <li><a href="#" data-url="https://attack.mitre.org/"><i class="fas fa-crosshairs"></i> MITRE ATT&CK</a></li>
                    <li><a href="#" data-url="https://gtfobins.github.io/"><i class="fas fa-terminal"></i> GTFOBins</a></li>
                    <li><a href="#" data-url="https://lolbas-project.github.io/"><i class="fas fa-windows"></i> LOLBAS</a></li>
                    <li><a href="#" data-url="https://book.hacktricks.xyz/"><i class="fas fa-book-open"></i> HackTricks</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <h3><i class="fas fa-cogs"></i> SYSTEM TOOLS</h3>
                <ul class="system-tools">
                    <li><button class="tool-btn" id="terminal-btn"><i class="fas fa-terminal"></i> Terminal</button></li>
                    <li><button class="tool-btn" id="notes-btn"><i class="fas fa-sticky-note"></i> Notes</button></li>
                    <li><button class="tool-btn" id="scanner-btn"><i class="fas fa-radar"></i> Port Scanner</button></li>
                    <li><button class="tool-btn" id="encoder-btn"><i class="fas fa-code"></i> Encoder/Decoder</button></li>
                </ul>
            </div>
        </nav>

        <!-- Content Area -->
        <main class="content-area">
            <!-- Browser Frame -->
            <div class="browser-frame">
                <div class="browser-header">
                    <div class="browser-controls">
                        <button class="browser-btn" id="back-btn"><i class="fas fa-arrow-left"></i></button>
                        <button class="browser-btn" id="forward-btn"><i class="fas fa-arrow-right"></i></button>
                        <button class="browser-btn" id="refresh-btn"><i class="fas fa-redo"></i></button>
                    </div>
                    <div class="address-bar">
                        <input type="url" id="url-input" placeholder="https://target.com" value="about:hackerbrowser">
                        <button id="go-btn"><i class="fas fa-arrow-right"></i></button>
                    </div>
                    <div class="browser-actions">
                        <button class="browser-btn" id="bookmark-btn"><i class="fas fa-bookmark"></i></button>
                        <button class="browser-btn" id="tools-btn"><i class="fas fa-cog"></i></button>
                    </div>
                </div>
                
                <!-- Tab Container -->
                <div class="tab-container">
                    <div class="tab active" data-content="reconnaissance">
                        <i class="fas fa-search"></i>
                        <span>Reconnaissance</span>
                        <button class="tab-close"><i class="fas fa-times"></i></button>
                    </div>
                    <button class="new-tab-btn"><i class="fas fa-plus"></i></button>
                </div>

                <!-- Content Frame -->
                <div class="content-frame">
                    <iframe id="browser-content" src="about:blank"></iframe>
                    
                    <!-- Default Landing Page -->
                    <div id="landing-page" class="landing-page active">
                        <div class="welcome-section">
                            <h1><i class="fas fa-skull"></i> HACKERBROWSER v2.1.0</h1>
                            <p class="subtitle">Your Gateway to the Digital Underground</p>
                            <div class="ascii-art">
                                <pre>
    ██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗ 
    ██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
    ███████║███████║██║     █████╔╝ █████╗  ██████╔╝
    ██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
    ██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
    ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
                                                    
                    BROWSER v2.1.0 - TERMINAL MODE
                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Resource Panel -->
        <aside class="resource-panel">
            <div class="panel-header">
                <h3><i class="fas fa-bookmark"></i> BOOKMARKS</h3>
                <button class="panel-toggle"><i class="fas fa-chevron-right"></i></button>
            </div>
            
            <div class="bookmark-sections" id="bookmark-content">
                <!-- Bookmark content will be loaded here -->
            </div>
        </aside>
    </div>

    <!-- Footer Status Bar -->
    <footer class="status-bar">
        <div class="status-left">
            <span class="status-item">
                <i class="fas fa-wifi"></i>
                <span id="connection-status">CONNECTED</span>
            </span>
            <span class="status-item">
                <i class="fas fa-shield-alt"></i>
                <span>VPN: ACTIVE</span>
            </span>
            <span class="status-item">
                <i class="fas fa-globe"></i>
                <span id="ip-address">IP: ***********00</span>
            </span>
        </div>
        <div class="status-right">
            <span class="status-item">
                <i class="fas fa-memory"></i>
                <span id="memory-usage">RAM: 2.1GB</span>
            </span>
            <span class="status-item">
                <i class="fas fa-microchip"></i>
                <span id="cpu-usage">CPU: 15%</span>
            </span>
            <span class="status-item">
                <i class="fas fa-hdd"></i>
                <span id="disk-usage">DISK: 45%</span>
            </span>
        </div>
    </footer>

    <!-- Terminal Modal -->
    <div id="terminal-modal" class="modal">
        <div class="modal-content terminal-window">
            <div class="modal-header">
                <h3><i class="fas fa-terminal"></i> HackerTerminal v1.0</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="terminal-output" id="terminal-output">
                <div class="terminal-line">
                    <span class="terminal-prompt">root@hackbox:~#</span>
                    <span class="terminal-text">Welcome to HackerTerminal v1.0</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">root@hackbox:~#</span>
                    <span class="terminal-text">Type 'help' for available commands</span>
                </div>
            </div>
            <div class="terminal-input-line">
                <span class="terminal-prompt">root@hackbox:~#</span>
                <input type="text" id="terminal-input" class="terminal-input" autocomplete="off">
            </div>
        </div>
    </div>

    <!-- Notes Modal -->
    <div id="notes-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-sticky-note"></i> Hacker Notes</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="notes-content">
                <textarea id="notes-textarea" placeholder="Enter your reconnaissance notes, findings, and observations here..."></textarea>
                <div class="notes-actions">
                    <button id="save-notes" class="action-btn"><i class="fas fa-save"></i> Save</button>
                    <button id="clear-notes" class="action-btn"><i class="fas fa-trash"></i> Clear</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Port Scanner Modal -->
    <div id="scanner-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-radar"></i> Port Scanner</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="scanner-content">
                <div class="scanner-input">
                    <input type="text" id="target-input" placeholder="Target IP/Domain (e.g., ***********)" class="scanner-field">
                    <input type="text" id="ports-input" placeholder="Ports (e.g., 80,443,22 or 1-1000)" class="scanner-field">
                    <button id="scan-btn" class="action-btn"><i class="fas fa-play"></i> Scan</button>
                </div>
                <div class="scanner-output" id="scanner-output">
                    <div class="scan-result">Ready to scan...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Encoder/Decoder Modal -->
    <div id="encoder-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-code"></i> Encoder/Decoder</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="encoder-content">
                <div class="encoder-controls">
                    <select id="encoding-type" class="encoder-select">
                        <option value="base64">Base64</option>
                        <option value="url">URL Encoding</option>
                        <option value="html">HTML Entities</option>
                        <option value="hex">Hexadecimal</option>
                        <option value="md5">MD5 Hash</option>
                        <option value="sha1">SHA1 Hash</option>
                        <option value="sha256">SHA256 Hash</option>
                    </select>
                    <button id="encode-btn" class="action-btn"><i class="fas fa-lock"></i> Encode</button>
                    <button id="decode-btn" class="action-btn"><i class="fas fa-unlock"></i> Decode</button>
                </div>
                <div class="encoder-io">
                    <textarea id="input-text" placeholder="Enter text to encode/decode..." class="encoder-textarea"></textarea>
                    <textarea id="output-text" placeholder="Output will appear here..." class="encoder-textarea" readonly></textarea>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
