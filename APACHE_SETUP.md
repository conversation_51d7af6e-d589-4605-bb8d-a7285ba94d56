# 🚀 HackerBrowser Apache Setup Guide

## 🎯 **Complete Chrome-Style Browser with Apache Hosting**

Your HackerBrowser now includes a full Chrome-style new tab page hosted on Apache web server!

## 📋 **What's Been Configured:**

### **🌐 Apache Web Server:**
- **Port:** 9000 (to avoid conflicts)
- **Document Root:** `/var/www/html/`
- **Default Page:** Chrome-style new tab (`newtab.html`)
- **Rewrite Module:** Enabled for better URL handling

### **🎨 Chrome-Style New Tab Features:**
- **Google-style Search:** Multiple search options including "Hacker Search"
- **Customizable Shortcuts:** Add/edit/remove security tool shortcuts
- **Quick Access Panels:** Organized by categories (Recon, Exploitation, OSINT, Learning)
- **Professional UI:** Chrome-inspired design with hacker aesthetics
- **Persistent Storage:** Shortcuts saved in browser localStorage

## 🔧 **Access URLs:**

### **Main Interfaces:**
```bash
# Chrome-style New Tab (Default)
http://localhost:9000/

# Original HackerBrowser Interface
http://localhost:9000/index.html

# Demo Page (iframe test)
http://localhost:9000/demo.html
```

## 🎮 **New Tab Page Features:**

### **🔍 Search Functionality:**
- **Google Search:** Standard web search
- **I'm Feeling Lucky:** Direct to first result
- **Hacker Search:** Multi-database security search
  - ExploitDB
  - CVE Database
  - Shodan
  - GitHub Security

### **⚡ Customizable Shortcuts:**
- **Default Tools:** Kali, ExploitDB, Shodan, GitHub, HackTheBox, etc.
- **Add Custom:** Click "Add shortcut" button
- **Right-click Menu:** Edit, remove, or open shortcuts
- **Icon Selection:** Choose from security-themed icons

### **📊 Quick Access Panels:**
1. **Reconnaissance:** Shodan, Censys, Nmap
2. **Exploitation:** Metasploit, ExploitDB, SQLmap
3. **OSINT:** Maltego, Sherlock, SpiderFoot
4. **Learning:** HackTheBox, TryHackMe, VulnHub

### **⌨️ Keyboard Shortcuts:**
- **Ctrl+K:** Focus search bar
- **Ctrl+T:** New tab (in main browser)
- **Esc:** Close modals

## 🛠️ **Integration with Main Browser:**

### **Navigation:**
- **New Tab Button:** Opens Chrome-style new tab
- **Address Bar:** Type `newtab.html` to access
- **Footer Link:** "Open HackerBrowser" returns to main interface

### **Seamless Workflow:**
1. Start with Chrome-style new tab
2. Search or select tools
3. Switch to main browser for advanced features
4. Use built-in tools (terminal, scanner, encoder)

## 🔧 **Apache Configuration Details:**

### **Port Configuration:**
```apache
# /etc/apache2/ports.conf
Listen 9000

# /etc/apache2/sites-available/000-default.conf
<VirtualHost *:9000>
```

### **Directory Index:**
```apache
# /var/www/html/.htaccess
DirectoryIndex newtab.html index.html
```

### **File Structure:**
```
/var/www/html/
├── newtab.html          # Chrome-style new tab
├── newtab.css           # New tab styling
├── newtab.js            # New tab functionality
├── index.html           # Main HackerBrowser
├── styles.css           # Main browser styling
├── script.js            # Main browser functionality
├── demo.html            # Demo page
└── .htaccess            # Apache configuration
```

## 🚀 **Usage Workflow:**

### **1. Start with New Tab:**
```bash
# Open browser to
http://localhost:9000/
```

### **2. Search or Browse:**
- Use search bar for web searches
- Click shortcuts for quick tool access
- Browse organized panels by category

### **3. Advanced Features:**
- Click "Open HackerBrowser" for full interface
- Access built-in tools (terminal, scanner, etc.)
- Use organized bookmark system

### **4. Customization:**
- Add your favorite security tools as shortcuts
- Organize by workflow preferences
- Customize with different icons

## 🎯 **Key Benefits:**

### **Professional Workflow:**
- ✅ Chrome-familiar interface
- ✅ Quick tool access
- ✅ Organized by security categories
- ✅ Persistent customization

### **Security Focus:**
- ✅ 600+ security tools organized
- ✅ Multi-database hacker search
- ✅ Built-in security utilities
- ✅ Professional penetration testing workflow

### **Modern Features:**
- ✅ Responsive design
- ✅ Keyboard shortcuts
- ✅ Context menus
- ✅ Real-time notifications

## 🔧 **Troubleshooting:**

### **Apache Issues:**
```bash
# Check Apache status
sudo systemctl status apache2

# Restart Apache
sudo systemctl restart apache2

# Check port availability
sudo netstat -tlnp | grep :9000
```

### **File Permissions:**
```bash
# Fix permissions if needed
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### **Port Conflicts:**
```bash
# If port 9000 is busy, change to another port
sudo sed -i 's/9000/9001/g' /etc/apache2/ports.conf
sudo sed -i 's/:9000>/:9001>/g' /etc/apache2/sites-available/000-default.conf
sudo systemctl restart apache2
```

## 🎉 **Success!**

Your HackerBrowser now features:
- 🎨 **Chrome-style new tab page**
- 🌐 **Professional Apache hosting**
- 🔧 **Integrated security tools**
- ⚡ **Customizable shortcuts**
- 🎯 **Professional workflow**

**Access your new Chrome-style hacker browser at:** `http://localhost:9000/`

---

**🔥 HackerBrowser v2.1.0 - Now with Chrome-style interface!** 🚀
